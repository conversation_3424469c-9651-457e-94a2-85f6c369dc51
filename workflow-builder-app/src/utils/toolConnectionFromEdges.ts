/**
 * Utility functions for generating tool connections from visual edges
 * This ensures tool nodes remain visible and editable on the canvas
 */

import { Node, Edge } from "reactflow";
import { WorkflowNodeData } from "@/types";

export interface ToolConnection {
  node_id: string;
  node_type: string;
  node_label: string;
  component_id: string;
  component_type: string;
  component_name: string;
  component_definition: any;
  component_config: any;
  mcp_metadata?: any;
}

export interface ToolConnectionsByHandle {
  [handleId: string]: ToolConnection[];
}

/**
 * Generate tool connections for an AgenticAI node from visual edges
 * @param agenticAINode The AgenticAI node to generate tool connections for
 * @param nodes All nodes in the workflow
 * @param edges All edges in the workflow
 * @returns Tool connections grouped by handle
 */
export function generateToolConnectionsFromEdges(
  agenticAINode: Node<WorkflowNodeData>,
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[]
): ToolConnectionsByHandle {
  // Find all edges connecting to this AgenticAI node's tool handles
  const toolEdges = edges.filter(edge =>
    edge.target === agenticAINode.id &&
    edge.targetHandle &&
    (edge.targetHandle.includes("tool") || edge.targetHandle === "tools")
  );

  if (toolEdges.length === 0) {
    return {};
  }

  console.log(`[TOOL CONNECTIONS] Found ${toolEdges.length} tool edges for AgenticAI node ${agenticAINode.id}`);

  // Group tool connections by handle
  const toolConnections: ToolConnectionsByHandle = {};

  toolEdges.forEach(edge => {
    const sourceNode = nodes.find(node => node.id === edge.source);
    if (sourceNode) {
      const handleId = edge.targetHandle || "tools"; // Default to "tools" handle

      if (!toolConnections[handleId]) {
        toolConnections[handleId] = [];
      }

      // Create tool connection data from the source node
      const toolConnection: ToolConnection = {
        node_id: sourceNode.id,
        node_type: sourceNode.data.originalType || sourceNode.data.type || "Unknown",
        node_label: sourceNode.data.label || sourceNode.data.definition?.display_name || "Unknown Tool",
        component_id: sourceNode.id,
        component_type: sourceNode.data.originalType || sourceNode.data.type || "Unknown",
        component_name: sourceNode.data.label || sourceNode.data.definition?.display_name || "Unknown Tool",
        component_definition: sourceNode.data.definition || {},
        component_config: sourceNode.data.config || {},
        // Add MCP metadata if available
        ...(sourceNode.data.definition?.mcp_info && {
          mcp_metadata: sourceNode.data.definition.mcp_info
        })
      };

      toolConnections[handleId].push(toolConnection);
      console.log(`[TOOL CONNECTIONS] Added tool connection: ${sourceNode.data.label} -> ${agenticAINode.data.label} (${handleId})`);
    }
  });

  return toolConnections;
}

/**
 * Generate tool connections for all AgenticAI nodes in a workflow
 * @param nodes All nodes in the workflow
 * @param edges All edges in the workflow
 * @returns Map of AgenticAI node IDs to their tool connections
 */
export function generateAllToolConnectionsFromEdges(
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[]
): Map<string, ToolConnectionsByHandle> {
  const allToolConnections = new Map<string, ToolConnectionsByHandle>();

  // Find all AgenticAI nodes
  const agenticAINodes = nodes.filter(node => node.data.originalType === "AgenticAI");

  agenticAINodes.forEach(agenticNode => {
    const toolConnections = generateToolConnectionsFromEdges(agenticNode, nodes, edges);
    if (Object.keys(toolConnections).length > 0) {
      allToolConnections.set(agenticNode.id, toolConnections);
    }
  });

  return allToolConnections;
}

/**
 * Update AgenticAI nodes with tool connections generated from edges
 * This is used during save/execution to ensure tool connections are preserved
 * @param nodes All nodes in the workflow
 * @param edges All edges in the workflow
 * @returns Updated nodes with tool connections in their config
 */
export function updateNodesWithToolConnectionsFromEdges(
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[]
): Node<WorkflowNodeData>[] {
  const allToolConnections = generateAllToolConnectionsFromEdges(nodes, edges);

  return nodes.map(node => {
    if (node.data.originalType === "AgenticAI" && allToolConnections.has(node.id)) {
      const toolConnections = allToolConnections.get(node.id)!;
      
      // Update the node's config with generated tool connections
      return {
        ...node,
        data: {
          ...node.data,
          config: {
            ...node.data.config,
            tool_connections: toolConnections
          }
        }
      };
    }
    return node;
  });
}

/**
 * Check if a handle is a tool handle
 * @param handleId The handle ID to check
 * @returns True if it's a tool handle
 */
export function isToolHandle(handleId: string): boolean {
  return handleId.includes("tool") || handleId === "tools";
}

/**
 * Convert tool connections back to visual nodes and edges
 * This is the reverse operation of generateToolConnectionsFromEdges
 * @param nodes All nodes in the workflow
 * @param edges All edges in the workflow
 * @returns Object containing additional nodes and edges to add to the workflow
 */
export function convertToolConnectionsToVisualComponents(
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[]
): { additionalNodes: Node<WorkflowNodeData>[]; additionalEdges: Edge[] } {
  const additionalNodes: Node<WorkflowNodeData>[] = [];
  const additionalEdges: Edge[] = [];

  console.log("[TOOL CONVERSION] Converting tool connections to visual components...");

  // Find all AgenticAI nodes with tool connections
  const agenticAINodes = nodes.filter(node =>
    node.data.originalType === "AgenticAI" &&
    node.data.config?.tool_connections
  );

  agenticAINodes.forEach(agenticNode => {
    const toolConnections = agenticNode.data.config.tool_connections;
    console.log(`[TOOL CONVERSION] Processing AgenticAI node ${agenticNode.id} with tool connections:`, toolConnections);

    // Process each handle's tool connections
    Object.entries(toolConnections).forEach(([handleId, connections]) => {
      if (Array.isArray(connections)) {
        connections.forEach((toolData: ToolConnection, index: number) => {
          // Check if this tool node already exists in the nodes array
          const existingNode = nodes.find(n => n.id === toolData.node_id);
          const existingAdditionalNode = additionalNodes.find(n => n.id === toolData.node_id);

          if (!existingNode && !existingAdditionalNode) {
            // Create a new tool node from the stored tool connection data
            const toolNode: Node<WorkflowNodeData> = {
              id: toolData.node_id,
              type: "WorkflowNode",
              position: {
                // Position tool nodes to the left of the AgenticAI node
                x: agenticNode.position.x - 300,
                y: agenticNode.position.y + (index * 150) - 100
              },
              data: {
                label: toolData.node_label || toolData.component_name || "Tool",
                type: toolData.component_type === "MCP" ? "mcp" : "component",
                originalType: toolData.node_type || toolData.component_type,
                definition: toolData.component_definition || {},
                config: toolData.component_config || {},
                // Mark as tool connection for visual differentiation
                isToolConnection: true
              },
              width: 208,
              height: 122,
              selected: false,
              dragging: false
            };

            additionalNodes.push(toolNode);
            console.log(`[TOOL CONVERSION] Created tool node: ${toolNode.id} (${toolNode.data.label})`);
          }

          // Check if edge already exists
          const edgeId = `${toolData.node_id}-${agenticNode.id}`;
          const existingEdge = edges.find(e => e.id === edgeId);
          const existingAdditionalEdge = additionalEdges.find(e => e.id === edgeId);

          if (!existingEdge && !existingAdditionalEdge) {
            // Determine the correct source handle from the component definition
            let sourceHandle = "output"; // Default
            if (toolData.component_definition?.outputs && Array.isArray(toolData.component_definition.outputs)) {
              const firstOutput = toolData.component_definition.outputs[0];
              if (firstOutput?.name) {
                sourceHandle = firstOutput.name;
              }
            }

            // Create edge connecting tool to AgenticAI node
            const toolEdge: Edge = {
              id: edgeId,
              source: toolData.node_id,
              sourceHandle: sourceHandle,
              target: agenticNode.id,
              targetHandle: handleId === "tools" ? "tools" : handleId, // Use the actual handle ID
              type: "default",
              animated: true
            };

            additionalEdges.push(toolEdge);
            console.log(`[TOOL CONVERSION] Created tool edge: ${toolData.node_id} -> ${agenticNode.id} (${handleId})`);
          }
        });
      }
    });
  });

  console.log(`[TOOL CONVERSION] Created ${additionalNodes.length} tool nodes and ${additionalEdges.length} tool edges`);

  return { additionalNodes, additionalEdges };
}



/**
 * Test function to verify tool connection generation
 * @param nodes Test nodes
 * @param edges Test edges
 */
export function testToolConnectionGeneration(
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[]
): void {
  console.log("=== Testing Tool Connection Generation ===");
  
  const agenticAINodes = nodes.filter(node => node.data.originalType === "AgenticAI");
  console.log(`Found ${agenticAINodes.length} AgenticAI nodes`);
  
  agenticAINodes.forEach(agenticNode => {
    const toolConnections = generateToolConnectionsFromEdges(agenticNode, nodes, edges);
    console.log(`AgenticAI node ${agenticNode.id} tool connections:`, toolConnections);
  });
  
  const allToolConnections = generateAllToolConnectionsFromEdges(nodes, edges);
  console.log("All tool connections:", allToolConnections);
  
  const updatedNodes = updateNodesWithToolConnectionsFromEdges(nodes, edges);
  const updatedAgenticNodes = updatedNodes.filter(node => node.data.originalType === "AgenticAI");
  updatedAgenticNodes.forEach(node => {
    console.log(`Updated AgenticAI node ${node.id} config:`, node.data.config);
  });
  
  console.log("=== Test Complete ===");
}
